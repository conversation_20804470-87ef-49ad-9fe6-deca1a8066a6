"use client";

import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import {
  LinktreeExtractResult,
  ValidationErrors,
  LinktreeFormData
} from "../types";
import { API_ENDPOINTS, templateOptions } from "../constants";

export function useLinktreeExtractor() {
  const [formData, setFormData] = useState<LinktreeFormData>({
    linktreeUrl: "",
    additionalInfo: "",
    selectedTemplate: "general",
    autoExtract: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [apiResponse, setApiResponse] = useState<LinktreeExtractResult | null>(null);

  const { toast } = useToast();

  // Form validation function
  const validateForm = (): boolean => {
    const errors: ValidationErrors = {};

    if (!formData.linktreeUrl.trim()) {
      errors.linktreeUrl = "Linktree URL or username is required";
    } else if (formData.linktreeUrl.trim().length < 3) {
      errors.linktreeUrl = "Please enter a valid Linktree URL or username";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const clearValidationError = (field: string) => {
    setValidationErrors(prev => ({ ...prev, [field]: "" }));
  };

  const updateFormData = (updates: Partial<LinktreeFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const handleExtract = async () => {
    setIsLoading(true);
    setError(null);
    setApiResponse(null);

    try {
      // Static version: Linktree extraction disabled
      if (!API_ENDPOINTS.EXTRACT) {
        throw new Error("Linktree extraction is not available in static version. Please configure NEXT_PUBLIC_FIRECRAWL_API_URL if you need this feature.");
      }

      const response = await fetch(API_ENDPOINTS.EXTRACT, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url: formData.linktreeUrl }),
      });

      if (!response.ok) {
        throw new Error("Failed to extract data");
      }

      const data = await response.json();
      // Handle FireCrawl response structure
      let extractedData;

      if (data.success && data.data && data.data.length > 0) {
        extractedData = data.data[0];
      } else if (data.data && data.data.length > 0) {
        extractedData = data.data[0];
      } else if (data.success === false && data.error) {
        extractedData = { error: data.error };
      } else {
        extractedData = data;
      }

      updateFormData({ additionalInfo: JSON.stringify(extractedData, null, 2) });
      setApiResponse(data);

      toast({
        title: "Success",
        description: "Linktree data extracted successfully.",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    // Clear previous errors
    setError(null);
    setValidationErrors({});

    // Validate form before submission
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before submitting.",
      });
      return;
    }

    if (formData.autoExtract) {
      await handleExtract();
    }

    setIsLoading(true);

    try {
      const response = await fetch(API_ENDPOINTS.PROCESS, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          user: formData.linktreeUrl.trim(),
          info: formData.additionalInfo,
          template_name: formData.selectedTemplate,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to submit to external API");
      }

      // Parse and store the API response
      const responseData = await response.json();
      setApiResponse(responseData);

      toast({
        title: "Success",
        description: `Data submitted successfully using ${templateOptions.find((t) => t.value === formData.selectedTemplate)?.label
          } template.`,
      });

      console.log(
        "Successfully submitted to external API with template:",
        formData.selectedTemplate,
        "Response:",
        responseData
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    formData,
    updateFormData,
    isLoading,
    error,
    validationErrors,
    apiResponse,
    clearValidationError,
    handleExtract,
    handleSubmit,
  };
}
