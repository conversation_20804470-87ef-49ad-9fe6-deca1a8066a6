// ============================================================================
// DATA SERVICE - Handles local JSON data files
// ============================================================================

import type { UserProfile } from '@/types'
import { validateUserProfile } from '@/types/validation'
import { transformLegacyUserData } from '@/lib/userUtils'

/**
 * Service for handling local JSON data files
 */
class DataService {
  private dataCache = new Map<string, UserProfile>()
  private cacheExpiry = new Map<string, number>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  /**
   * Maps usernames to their corresponding data files
   */
  private getUsernameToFileMap(): Record<string, string> {
    return {
      'chadombarber': 'data',
      'andreluiztattoo': 'new',
      'barbeariatarantino': 'barbearia',
      'rocusbarbearia': 'rocusbarbearia'
    }
  }

  /**
   * Loads user data from local JSON file
   */
  async loadUserData(username: string): Promise<UserProfile | null> {
    try {
      // Check cache first
      if (this.isCacheValid(username)) {
        const cachedData = this.dataCache.get(username)
        if (cachedData) {
          console.log(`Returning cached data for ${username}`)
          return cachedData
        }
      }

      // Map username to data file
      const usernameToFile = this.getUsernameToFileMap()
      const filename = usernameToFile[username] || username

      // Try to load from data directory
      const response = await fetch(`/data/${filename}.json`)

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`Data file not found for ${username}`)
          return null
        }
        throw new Error(`Failed to load data: ${response.status}`)
      }

      // Safely parse JSON response
      let rawData
      try {
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          rawData = await response.json()
        } else {
          console.warn(`Expected JSON but received ${contentType} for ${username}`)
          return null
        }
      } catch (parseError) {
        console.error(`Failed to parse JSON response for ${username}:`, parseError)
        return null
      }
      console.log(`Loaded raw data for ${username}`)

      // Transform and validate data
      const userData = transformLegacyUserData(rawData)
      const validation = validateUserProfile(userData)

      if (!validation.isValid) {
        console.warn(`Invalid user data for ${username}:`, validation.errors)
        // Continue with data but log warnings
      }

      // Cache the data
      this.cacheData(username, userData)

      return userData
    } catch (error) {
      console.error(`Error loading user data for ${username}:`, error)
      return null
    }
  }

  /**
   * Gets all available usernames from the data directory
   */
  async getAvailableUsers(): Promise<string[]> {
    try {
      // Map of data files to their usernames
      const dataFileToUsername = {
        'data': 'chadombarber',
        'new': 'andreluiztattoo',
        'barbearia': 'barbeariatarantino',
        'rocusbarbearia': 'rocusbarbearia'
      }

      const existingUsers: string[] = []

      // Check each data file and extract the username
      for (const [filename, expectedUsername] of Object.entries(dataFileToUsername)) {
        try {
          const response = await fetch(`/data/${filename}.json`)
          if (response.ok) {
            const data = await response.json()
            if (data.user && data.user.username) {
              existingUsers.push(data.user.username)
            }
          }
        } catch (error) {
          console.warn(`Failed to load ${filename}.json:`, error)
        }
      }

      return existingUsers
    } catch (error) {
      console.error('Error getting available users:', error)
      return []
    }
  }

  /**
   * Searches users by name or username
   */
  async searchUsers(query: string): Promise<UserProfile[]> {
    try {
      const availableUsers = await this.getAvailableUsers()
      const results: UserProfile[] = []

      for (const username of availableUsers) {
        const userData = await this.loadUserData(username)
        if (userData) {
          const matchesUsername = userData.user.username.toLowerCase().includes(query.toLowerCase())
          const matchesName = userData.user.name.toLowerCase().includes(query.toLowerCase())
          const matchesBio = userData.user.bio.toLowerCase().includes(query.toLowerCase())

          if (matchesUsername || matchesName || matchesBio) {
            results.push(userData)
          }
        }
      }

      return results
    } catch (error) {
      console.error('Error searching users:', error)
      return []
    }
  }

  /**
   * Gets user statistics
   */
  async getUserStats(): Promise<{
    totalUsers: number
    totalLinks: number
    totalSections: number
  }> {
    try {
      const availableUsers = await this.getAvailableUsers()
      let totalLinks = 0
      let totalSections = 0

      for (const username of availableUsers) {
        const userData = await this.loadUserData(username)
        if (userData) {
          totalLinks += userData.links.length + userData.socialMedia.length

          // Count enabled sections
          if (userData.featuresSection.enabled) totalSections++
          if (userData.servicesSection.enabled) totalSections++
          if (userData.genericSection.enabled) totalSections++
          if (userData.gallery.enabled) totalSections++
          if (userData.reviews.enabled) totalSections++
          if (userData.video.enabled) totalSections++
        }
      }

      return {
        totalUsers: availableUsers.length,
        totalLinks,
        totalSections
      }
    } catch (error) {
      console.error('Error getting user stats:', error)
      return {
        totalUsers: 0,
        totalLinks: 0,
        totalSections: 0
      }
    }
  }

  /**
   * Validates data file structure
   */
  async validateDataFile(username: string): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    try {
      const userData = await this.loadUserData(username)

      if (!userData) {
        return {
          isValid: false,
          errors: ['User data not found'],
          warnings: []
        }
      }

      const validation = validateUserProfile(userData)

      return {
        isValid: validation.isValid,
        errors: validation.errors.map(error => `${error.field}: ${error.message}`),
        warnings: []
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        warnings: []
      }
    }
  }

  /**
   * Preloads data for multiple users
   */
  async preloadUsers(usernames: string[]): Promise<void> {
    const loadPromises = usernames.map(username => this.loadUserData(username))
    await Promise.allSettled(loadPromises)
    console.log(`Preloaded data for ${usernames.length} users`)
  }

  /**
   * Clears cache for a specific user or all users
   */
  clearCache(username?: string): void {
    if (username) {
      this.dataCache.delete(username)
      this.cacheExpiry.delete(username)
      console.log(`Cleared cache for ${username}`)
    } else {
      this.dataCache.clear()
      this.cacheExpiry.clear()
      console.log('Cleared all cache')
    }
  }

  /**
   * Gets cache statistics
   */
  getCacheStats(): {
    cachedUsers: string[]
    cacheSize: number
    oldestEntry?: string
    newestEntry?: string
  } {
    const cachedUsers = Array.from(this.dataCache.keys())
    const now = Date.now()

    let oldestEntry: string | undefined
    let newestEntry: string | undefined
    let oldestTime = now
    let newestTime = 0

    for (const [username, expiry] of this.cacheExpiry.entries()) {
      const cacheTime = expiry - this.CACHE_DURATION
      if (cacheTime < oldestTime) {
        oldestTime = cacheTime
        oldestEntry = username
      }
      if (cacheTime > newestTime) {
        newestTime = cacheTime
        newestEntry = username
      }
    }

    return {
      cachedUsers,
      cacheSize: this.dataCache.size,
      oldestEntry,
      newestEntry
    }
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private isCacheValid(username: string): boolean {
    const expiry = this.cacheExpiry.get(username)
    if (!expiry) return false

    return Date.now() < expiry
  }

  private cacheData(username: string, data: UserProfile): void {
    this.dataCache.set(username, data)
    this.cacheExpiry.set(username, Date.now() + this.CACHE_DURATION)
  }

  private cleanExpiredCache(): void {
    const now = Date.now()

    for (const [username, expiry] of this.cacheExpiry.entries()) {
      if (now >= expiry) {
        this.dataCache.delete(username)
        this.cacheExpiry.delete(username)
      }
    }
  }
}

// Create singleton instance
export const dataService = new DataService()

// Clean expired cache every 10 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    dataService['cleanExpiredCache']()
  }, 10 * 60 * 1000)
}

export default dataService
