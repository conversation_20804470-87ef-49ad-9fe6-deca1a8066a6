import { User<PERSON><PERSON><PERSON><PERSON>, UserNotFoundError, InvalidUsernameError } from '@/types'
import fs from 'fs'
import path from 'path'

/**
 * Service for handling local JSON data instead of Firebase
 */
class LocalDataService {
  private cachedData: UserProfile | null = null

  /**
   * Load data from local JSON file
   */
  async loadLocalData(): Promise<UserProfile | null> {
    try {
      if (this.cachedData) {
        return this.cachedData
      }

      let data: any

      // Check if we're running on the server (Node.js) or client (browser)
      if (typeof window === 'undefined') {
        // Server-side: read file directly from filesystem
        const filePath = path.join(process.cwd(), 'public', 'data', 'data.json')
        const fileContent = fs.readFileSync(filePath, 'utf8')
        data = JSON.parse(fileContent)
      } else {
        // Client-side: use fetch
        const response = await fetch('/data/data.json')
        if (!response.ok) {
          console.error(`Failed to load local data: ${response.status}`)
          return null
        }
        data = await response.json()
      }

      // Validate that we have the expected structure
      if (!data || typeof data !== 'object') {
        console.error('Invalid data format in local JSON file')
        return null
      }

      // Cache the data
      this.cachedData = data as UserProfile
      return this.cachedData
    } catch (error) {
      console.error('Error loading local data:', error)
      return null
    }
  }

  /**
   * Get user profile by username
   * Since we only have one profile in the local file, we'll match against the username in the data
   */
  async getUserProfile(username: string): Promise<UserProfile> {
    if (!username || username.length < 2) {
      throw new InvalidUsernameError(username)
    }

    try {
      const data = await this.loadLocalData()

      if (!data) {
        throw new UserNotFoundError(username)
      }

      // Check if the requested username matches the one in our data
      if (data.user.username !== username) {
        throw new UserNotFoundError(username)
      }

      return data
    } catch (error) {
      if (error instanceof UserNotFoundError || error instanceof InvalidUsernameError) {
        throw error
      }
      console.error(`Error fetching user profile for ${username}:`, error)
      throw new UserNotFoundError(username)
    }
  }

  /**
   * Get the default username from the local data
   */
  async getDefaultUsername(): Promise<string | null> {
    try {
      const data = await this.loadLocalData()
      return data?.user.username || null
    } catch (error) {
      console.error('Error getting default username:', error)
      return null
    }
  }

  /**
   * Check if a username exists in our local data
   */
  async userExists(username: string): Promise<boolean> {
    try {
      const data = await this.loadLocalData()
      return data?.user.username === username
    } catch (error) {
      return false
    }
  }

  /**
   * Clear cache (useful for development)
   */
  clearCache(): void {
    this.cachedData = null
  }
}

export const localDataService = new LocalDataService()
