"use client";

import React from "react";
import Image from "next/image";
import { ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PLACEHOLDER_IMAGES } from "@/types/constants";
import { getIconComponent } from "@/lib/iconUtils";
import { SectionCardProps } from "./types";

export const SectionCard: React.FC<SectionCardProps> = ({
  item,
  buttonConfig,
  onButtonClick,
}) => {
  const hasPrimaryButton =
    item.primaryButton?.url && item.primaryButton.url !== "#";
  const hasSecondaryButton =
    item.secondaryButton?.url && item.secondaryButton.url !== "#";

  // Get the icon component for secondary button
  const SecondaryButtonIcon = item.secondaryButton?.icon
    ? getIconComponent(item.secondaryButton.icon)
    : ExternalLink;

  return (
    <div
      key={item.id}
      className="relative overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform-gpu group rounded-xl h-96 sm:h-80 lg:h-96"
    >
      <Image
        src={item.image || PLACEHOLDER_IMAGES.GALLERY_IMAGE}
        alt={item.title}
        fill
        className="object-cover transition-transform duration-700 group-hover:scale-110"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        priority={false}
      />
      {/* Enhanced gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"></div>

      {/* Badge for content */}
      {buttonConfig.showBadge && (
        <div className="absolute top-6 right-6">
          <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center transform transition-all duration-300 group-hover:scale-110 shadow-lg">
            <span className="text-white text-base font-bold">{item.id}</span>
          </div>
        </div>
      )}

      {/* Content positioned at bottom */}
      <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8">
        <h3 className="text-white font-bold text-xl sm:text-2xl mb-2 drop-shadow-lg">
          {item.title}
        </h3>
        <p className="text-white/90 text-sm sm:text-base mb-4 leading-relaxed drop-shadow-sm line-clamp-3">
          {item.description}
        </p>

        <div className="flex gap-3">
          {hasPrimaryButton && (
            <Button
              variant="default"
              size="lg"
              className="flex-1 h-14 bg-white/20 hover:bg-white/30 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl border-0 rounded-2xl text-white"
              onClick={(e) => onButtonClick(item.primaryButton?.url || "#", e)}
            >
              {buttonConfig.primaryButtonText}
            </Button>
          )}
          {hasSecondaryButton && (
            <Button
              variant="outline"
              size="lg"
              className={`${
                hasPrimaryButton ? "w-14 h-14 p-0" : "flex-1"
              } bg-white/10 border-white/30 hover:bg-white/20 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg rounded-2xl text-white`}
              onClick={(e) =>
                onButtonClick(item.secondaryButton?.url || "#", e)
              }
            >
              <SecondaryButtonIcon
                className={`${
                  hasPrimaryButton ? "w-5 h-5" : "w-4 h-4 mr-2"
                } group-hover:scale-110 transition-transform duration-300`}
                aria-hidden="true"
              />
              {!hasPrimaryButton && buttonConfig.secondaryButtonText}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
